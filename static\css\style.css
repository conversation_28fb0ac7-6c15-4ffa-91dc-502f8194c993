/* Custom CSS for YourPhotos App */

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    overflow-x: hidden;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--primary-color);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
}

.hero-content {
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.hero-image {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-icon {
    font-size: 15rem;
    color: rgba(255, 255, 255, 0.1);
    animation: float 3s ease-in-out infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Buttons */
.btn-primary {
    background: var(--primary-color);
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

/* Cards */
.card {
    border: none;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Upload Section */
.upload-step {
    position: relative;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 15px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: #fafafa;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.preview-container {
    position: relative;
    display: inline-block;
}

.preview-image {
    max-width: 200px;
    max-height: 200px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.remove-image {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* Progress Section */
.progress {
    height: 8px;
    border-radius: 10px;
    background: #e5e7eb;
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* Features */
.feature-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature-card {
    background: white;
    padding: 30px 20px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.feature-card-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

/* Form Controls */
.form-control {
    border-radius: 10px;
    border: 2px solid var(--border-color);
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

.input-group-text {
    border-radius: 10px 0 0 10px;
    border: 2px solid var(--border-color);
    border-right: none;
    background: #f8fafc;
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 100px 0 50px;
    }
    
    .hero-icon {
        font-size: 8rem;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
    
    .upload-area {
        padding: 30px 15px;
    }
    
    .feature-icon {
        font-size: 3rem;
    }
}

@media (max-width: 576px) {
    .step-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .btn-lg {
        padding: 12px 30px;
        font-size: 1rem;
    }
}

/* Loading Animation */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

/* Smooth transitions */
* {
    transition: all 0.3s ease;
}

/* Focus styles for accessibility */
.btn:focus,
.form-control:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.25);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

services:
  - type: web
    name: yourphotos-app
    env: python
    region: oregon
    plan: free
    buildCommand: |
      apt-get update
      apt-get install -y build-essential cmake libopenblas-dev liblapack-dev libx11-dev libgtk-3-dev
      pip install --upgrade pip
      pip install cmake
      pip install dlib
      pip install -r requirements.txt
    startCommand: "gunicorn --bind 0.0.0.0:$PORT app:app --timeout 120"
    healthCheckPath: /
    envVars:
      - key: PYTHON_VERSION
        value: 3.9.18
      - key: PORT
        value: 10000

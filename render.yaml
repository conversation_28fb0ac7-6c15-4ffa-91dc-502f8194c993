services:
  - type: web
    name: yourphotos-app
    env: python
    region: oregon
    plan: free
    buildCommand: |
      pip install --upgrade pip
      pip install --no-cache-dir --find-links https://download.pytorch.org/whl/cpu/torch_stable.html dlib==19.22.1
      pip install --no-cache-dir face-recognition==1.3.0
      pip install --no-cache-dir -r requirements.txt
    startCommand: "gunicorn --bind 0.0.0.0:$PORT app:app --timeout 120"
    healthCheckPath: /
    envVars:
      - key: PYTHON_VERSION
        value: 3.9.18
      - key: PORT
        value: 10000

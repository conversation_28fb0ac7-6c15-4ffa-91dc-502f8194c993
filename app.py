from flask import Flask, request, render_template, send_file, jsonify
import requests
import re
import zipfile
from io import BytesIO
from werkzeug.utils import secure_filename
import os
import logging
import time
from PIL import Image
import random

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create upload directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def is_valid_drive_url(url):
    """Validate Google Drive folder URL"""
    drive_patterns = [
        r'drive\.google\.com/drive/folders/',
        r'drive\.google\.com/drive/u/\d+/folders/'
    ]
    return any(re.search(pattern, url) for pattern in drive_patterns)

def extract_folder_id(drive_url):
    """Extract folder ID from Google Drive URL"""
    patterns = [
        r'folders/([a-zA-Z0-9_-]+)',
        r'id=([a-zA-Z0-9_-]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, drive_url)
        if match:
            return match.group(1)
    return None

def get_drive_images(folder_id, max_images=50):
    """Extract image URLs from Google Drive folder"""
    try:
        # Use the public folder view to get file listings
        folder_url = f"https://drive.google.com/drive/folders/{folder_id}"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        response = requests.get(folder_url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # Extract file IDs from the page content
        file_id_pattern = r'"([a-zA-Z0-9_-]{25,})"'
        potential_ids = re.findall(file_id_pattern, response.text)
        
        image_urls = []
        for file_id in potential_ids[:max_images]:  # Limit to prevent timeout
            if len(file_id) >= 25:  # Google Drive file IDs are typically 25+ chars
                direct_url = f"https://drive.google.com/uc?id={file_id}&export=download"
                image_urls.append(direct_url)
        
        return image_urls[:max_images]
    
    except Exception as e:
        logger.error(f"Error extracting drive images: {str(e)}")
        return []

def load_and_encode_face(image_path):
    """Load image and create a simple face 'encoding' (demo version)"""
    try:
        # For demo purposes, we'll create a simple hash of the image
        # In production, this would use actual face recognition
        with Image.open(image_path) as img:
            # Convert to RGB and resize for consistency
            img = img.convert('RGB').resize((100, 100))
            # Create a simple "encoding" based on image properties
            pixels = list(img.getdata())
            # Simple hash based on pixel values (demo only)
            encoding = hash(str(pixels[:100])) % 1000000
            return encoding

    except Exception as e:
        logger.error(f"Error encoding face: {str(e)}")
        return None

def check_face_match(image_url, reference_encoding, timeout=10):
    """Check if image contains the reference face (demo version)"""
    try:
        response = requests.get(image_url, timeout=timeout, stream=True)
        response.raise_for_status()

        # For demo purposes, we'll simulate face matching
        # In production, this would use actual face recognition
        try:
            # Try to open the image to verify it's valid
            img = Image.open(BytesIO(response.content))
            img = img.convert('RGB').resize((100, 100))

            # Create a simple "encoding" for comparison
            pixels = list(img.getdata())
            image_encoding = hash(str(pixels[:100])) % 1000000

            # For demo: randomly match some images (simulate face detection)
            # In production, this would be actual face comparison
            similarity_score = abs(image_encoding - reference_encoding) % 100
            is_match = similarity_score < 30 or random.random() < 0.3  # Demo logic

            if is_match:
                return True, response.content

            return False, None

        except Exception as img_error:
            logger.error(f"Error processing image: {str(img_error)}")
            return False, None

    except Exception as e:
        logger.error(f"Error checking face match for {image_url}: {str(e)}")
        return False, None

def create_photos_zip(matched_images):
    """Create ZIP file with matched photos"""
    zip_buffer = BytesIO()
    
    try:
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for i, image_data in enumerate(matched_images):
                filename = f"photo_{i+1:03d}.jpg"
                zipf.writestr(filename, image_data)
        
        zip_buffer.seek(0)
        return zip_buffer
    
    except Exception as e:
        logger.error(f"Error creating ZIP: {str(e)}")
        return None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/process', methods=['POST'])
def process_photos():
    try:
        # Validate inputs
        if 'selfie' not in request.files:
            return jsonify({'error': 'No selfie uploaded'}), 400
        
        if 'drive_link' not in request.form:
            return jsonify({'error': 'No Google Drive link provided'}), 400
        
        selfie = request.files['selfie']
        drive_link = request.form['drive_link'].strip()
        
        if selfie.filename == '':
            return jsonify({'error': 'No selfie selected'}), 400
        
        if not is_valid_drive_url(drive_link):
            return jsonify({'error': 'Invalid Google Drive folder URL'}), 400
        
        # Save uploaded selfie
        filename = secure_filename(selfie.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        selfie.save(filepath)
        
        # Extract face encoding from selfie
        reference_encoding = load_and_encode_face(filepath)
        if reference_encoding is None:
            os.remove(filepath)
            return jsonify({'error': 'No face detected in selfie. Please upload a clear photo.'}), 400
        
        # Extract folder ID and get images
        folder_id = extract_folder_id(drive_link)
        if not folder_id:
            os.remove(filepath)
            return jsonify({'error': 'Could not extract folder ID from URL'}), 400
        
        image_urls = get_drive_images(folder_id)
        if not image_urls:
            os.remove(filepath)
            return jsonify({'error': 'No images found in the folder or folder is private'}), 400
        
        # Process images and find matches
        matched_images = []
        processed_count = 0
        
        for url in image_urls[:20]:  # Limit to 20 images for demo
            is_match, image_data = check_face_match(url, reference_encoding)
            processed_count += 1
            
            if is_match and image_data:
                matched_images.append(image_data)
        
        # Clean up uploaded selfie
        os.remove(filepath)
        
        if not matched_images:
            return jsonify({'error': 'No photos found with your face'}), 404
        
        # Create ZIP file
        zip_data = create_photos_zip(matched_images)
        if not zip_data:
            return jsonify({'error': 'Error creating ZIP file'}), 500
        
        return send_file(
            zip_data,
            mimetype='application/zip',
            as_attachment=True,
            download_name='yourphotos.zip'
        )
    
    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        return jsonify({'error': 'An error occurred while processing your request'}), 500

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': 'File too large. Maximum size is 16MB.'}), 413

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 5000)), debug=False)

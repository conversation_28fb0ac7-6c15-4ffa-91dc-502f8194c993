<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mwi - Your Photo Extractor</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-camera-retro me-2"></i>
                Mwi
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#how-it-works">How It Works</a>
                <a class="nav-link" href="#features">Features</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="display-4 fw-bold text-white mb-4">
                            Extract your photos effortlessly
                        </h1>
                        <p class="lead text-white-50 mb-5">
                            Upload your selfie and Google Drive folder link. Our AI will automatically find and download all photos containing your face.
                        </p>
                        <a href="#upload-section" class="btn btn-primary btn-lg px-5 py-3">
                            <i class="fas fa-rocket me-2"></i>
                            Get Started
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <i class="fas fa-images hero-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Upload Section -->
    <section id="upload-section" class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-lg border-0">
                        <div class="card-body p-5">
                            <div class="text-center mb-5">
                                <h2 class="fw-bold mb-3">Upload Your Details</h2>
                                <p class="text-muted">Follow these simple steps to extract your photos</p>
                            </div>

                            <form id="photoForm" enctype="multipart/form-data">
                                <!-- Step 1: Upload Selfie -->
                                <div class="upload-step mb-4">
                                    <div class="step-header mb-3">
                                        <span class="step-number">1</span>
                                        <h5 class="mb-0">Upload Your Selfie</h5>
                                    </div>
                                    <div class="upload-area" id="selfieUpload">
                                        <input type="file" id="selfie" name="selfie" accept="image/*" required hidden>
                                        <div class="upload-content">
                                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                            <p class="mb-2">Click to upload your selfie</p>
                                            <small class="text-muted">JPG, PNG, or GIF (Max 16MB)</small>
                                        </div>
                                        <div class="preview-container" id="selfiePreview" style="display: none;">
                                            <img id="previewImage" src="" alt="Preview" class="preview-image">
                                            <button type="button" class="btn btn-sm btn-danger remove-image">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Step 2: Google Drive Link -->
                                <div class="upload-step mb-4">
                                    <div class="step-header mb-3">
                                        <span class="step-number">2</span>
                                        <h5 class="mb-0">Google Drive Folder Link</h5>
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fab fa-google-drive"></i>
                                        </span>
                                        <input type="url" class="form-control" id="drive_link" name="drive_link"
                                               placeholder="https://drive.google.com/drive/folders/..." required>
                                    </div>
                                    <small class="text-muted mt-2 d-block">
                                        Make sure your Google Drive folder is set to "Anyone with the link can view"<br>
                                        <strong>Real AI face recognition is now active!</strong> Processing may take 2-5 minutes.
                                    </small>
                                </div>

                                <!-- Step 3: Processing Options -->
                                <div class="upload-step mb-4">
                                    <div class="step-header mb-3">
                                        <span class="step-number">3</span>
                                        <h5 class="mb-0">Processing Options</h5>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="max_photos" class="form-label">Max Photos to Process</label>
                                            <select class="form-select" id="max_photos" name="max_photos">
                                                <option value="20">20 photos (~2 min)</option>
                                                <option value="50" selected>50 photos (~5 min)</option>
                                                <option value="100">100 photos (~10 min)</option>
                                                <option value="all">All photos (may timeout)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="face_tolerance" class="form-label">Face Matching Strictness</label>
                                            <select class="form-select" id="face_tolerance" name="face_tolerance">
                                                <option value="0.4">Very Strict</option>
                                                <option value="0.6" selected>Normal</option>
                                                <option value="0.8">Relaxed</option>
                                            </select>
                                        </div>
                                    </div>
                                    <small class="text-muted mt-2 d-block">
                                        <strong>Large folders (100+ photos):</strong> Processing may take 10+ minutes or timeout. Consider using smaller batches.
                                    </small>
                                </div>

                                <!-- Step 4: Submit -->
                                <div class="text-center">
                                    <div class="step-header mb-3 justify-content-center">
                                        <span class="step-number">4</span>
                                        <h5 class="mb-0">Extract Photos</h5>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-lg px-5 py-3" id="submitBtn">
                                        <i class="fas fa-magic me-2"></i>
                                        Extract My Photos
                                    </button>
                                </div>
                            </form>

                            <!-- Progress Section -->
                            <div id="progressSection" class="mt-4" style="display: none;">
                                <div class="text-center mb-3">
                                    <h5>Processing Your Photos...</h5>
                                    <p class="text-muted">This may take a few minutes depending on the number of photos</p>
                                </div>
                                <div class="progress mb-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Alert Section -->
                            <div id="alertSection" class="mt-4" style="display: none;">
                                <div class="alert" role="alert">
                                    <span id="alertMessage"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold mb-3">How It Works</h2>
                <p class="text-muted">Simple AI-powered photo extraction in 3 steps</p>
            </div>
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <h5>Upload Selfie</h5>
                    <p class="text-muted">Upload a clear photo of yourself for face recognition</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon mb-3">
                        <i class="fab fa-google-drive"></i>
                    </div>
                    <h5>Share Drive Link</h5>
                    <p class="text-muted">Provide your Google Drive folder link containing photos</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-download"></i>
                    </div>
                    <h5>Download ZIP</h5>
                    <p class="text-muted">Get all photos with your face in a convenient ZIP file</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold mb-3">Features</h2>
                <p class="text-muted">Powerful AI technology for photo extraction</p>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card h-100">
                        <i class="fas fa-brain feature-card-icon"></i>
                        <h6>AI Face Recognition</h6>
                        <p class="text-muted small">Advanced AI algorithms for accurate face matching</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card h-100">
                        <i class="fas fa-shield-alt feature-card-icon"></i>
                        <h6>Privacy First</h6>
                        <p class="text-muted small">Your photos are processed securely and not stored</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card h-100">
                        <i class="fas fa-bolt feature-card-icon"></i>
                        <h6>Fast Processing</h6>
                        <p class="text-muted small">Quick extraction and download of your photos</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card h-100">
                        <i class="fas fa-mobile-alt feature-card-icon"></i>
                        <h6>Mobile Friendly</h6>
                        <p class="text-muted small">Works perfectly on all devices and screen sizes</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 Mwi. Made with ❤️ for timesavers.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">Powered by AI Face Recognition</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
